<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfsavecontent variable="local.sponsorJS">
	<cfoutput>
	<cfif local.qrySponsor.recordCount AND local.featureImageConfigID gt 0>
		<script type="text/javascript" src="/assets/admin/javascript/featuredImages.js#local.assetCachingKey#"></script>
	</cfif>
	<script type="text/javascript">
		function saveSponsorForm() {
			var arrReq = [];
			var sponsorContent = "", sponsorURL = "";
			mca_hideAlert('err_sponsor');

			if ($('##sponsorName').val() == '') arrReq.push('Enter the name of sponsor.');
			
			<cfif local.qrySponsor.recordCount>
				var urlRegEx = new RegExp("#application.regEx.url#", "gi");
				sponsorURL = $('##sponsorURL').val().trim();
				if (sponsorURL.length > 0 && !(urlRegEx.test(sponsorURL))) arrReq.push('Enter a valid Sponsor URL.');

				if(CKEDITOR.instances['sponsorContent'] != null)
					sponsorContent = CKEDITOR.instances['sponsorContent'].getData().trim();
				else 
					sponsorContent = $('textarea[name="sponsorContent"]').val().trim();
			</cfif>

			if (arrReq.length){
				mca_showAlert('err_sponsor', arrReq.join('<br />'),true);
				return false;
			}

			var saveSponsorResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					top.loadSponsors_#arguments.widgetSelectorID#();
					top.MCModalUtils.hideModal();
				} else {
					alert('Unable to #local.qrySponsor.recordCount ? 'save' : 'add'# this sponsor.');
					top.$('##btnMCModalSave').prop('disabled',false);
				}
			};
			
			top.$('##btnMCModalSave').prop('disabled',true);

			var objParams = { sponsorID:#val(local.qrySponsor.sponsorID)#, referenceType:'#arguments.referenceType#', referenceID:#arguments.referenceID#,
				sponsorName:$('##sponsorName').val().trim(), sponsorContent:sponsorContent,	sponsorURL:sponsorURL };

			<cfif local.qrySponsor.recordCount>
				// Include group assignment for existing sponsors in all contexts
				objParams.sponsorGroupingID = $('##sponsorGroupingID').val();
			</cfif>

			TS_AJX('SPONSORS','saveSponsor',objParams,saveSponsorResult,saveSponsorResult,10000,saveSponsorResult);
		}

		<cfif local.qrySponsor.recordCount and local.featureImageConfigID gt 0>
			function editSponsorImage() {
				let fext = '#local.arrConfigs[1].ftdExt#';
				top.editSponsorImage_#arguments.widgetSelectorID#(#val(local.qrySponsor.sponsorID)#,window['mcftd_formTitle'+fext],window['mcftd_editFeaturedImage'+fext+'Link']);
			}
		</cfif>
	</script>
	<cfif local.qrySponsor.recordCount and local.featureImageConfigID gt 0>
		#local.strFeaturedImages.js#
	</cfif>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.sponsorJS)#">

<cfoutput>
<form name="frmSponsor" id="frmSponsor" class="p-3">
	<div id="err_sponsor" class="alert alert-danger mb-2 d-none"></div>

	<div class="form-label-group mb-2">
		<input type="text" name="sponsorName" id="sponsorName" value="#local.qrySponsor.sponsorName#" class="form-control" autocomplete="off" maxlength="200">
		<label for="sponsorName">Name of Sponsor</label>
	</div>

	<!--- Group Assignment Dropdown (for all contexts) --->
	<cfif local.qrySponsor.recordCount AND local.qryGroupings.recordCount>
		<div class="form-group">
			<div class="form-label-group mb-3">
				<select name="sponsorGroupingID" id="sponsorGroupingID" class="form-control">
					<cfloop query="local.qryGroupings">
						<option value="#sponsorGroupingID#"<cfif (local.currentGroupingID EQ sponsorGroupingID) OR (local.currentGroupingID EQ 0 AND sponsorGroupingID EQ 0)> selected</cfif>>#encodeForHTML(sponsorGrouping)#</option>
					</cfloop>
				</select>
				<label for="sponsorGroupingID">Sponsor Group Assignment</label>
			</div>
		</div>
	</cfif>
	
	<cfif local.qrySponsor.recordCount>
		<div class="form-group row mb-3">
			<div class="col">
				#application.objWebEditor.showContentBoxWithLinks(fieldname='sponsorContent', fieldlabel='Sponsor Content', 
					contentID=local.qrySponsor.sponsorContentID, content=local.qrySponsor.sponsorContent, 
					allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
			</div>
		</div>
		<div class="form-label-group mb-4">
			<input type="text" name="sponsorURL" id="sponsorURL" value="#local.qrySponsor.sponsorURL#" class="form-control" autocomplete="off" maxlength="200">
			<label for="sponsorURL">Sponsor URL</label>
		</div>
		<cfif local.featureImageConfigID gt 0>
			#local.strFeaturedImages.html#
		</cfif>
	</cfif>
</form>
</cfoutput>
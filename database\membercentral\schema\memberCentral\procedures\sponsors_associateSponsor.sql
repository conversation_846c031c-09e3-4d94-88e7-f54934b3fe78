ALTER PROC dbo.sponsors_associateSponsor
@siteID INT,
@sponsorID INT,
@referenceType VARCHAR(20),
@referenceID INT,
@recordedByMemberID INT,
@sponsorGroupingID INT = NULL

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	DECLARE @orgID INT;

	-- verify sponsorID is part of site
	IF NOT EXISTS (SELECT sponsorID FROM dbo.sponsors WHERE siteID = @siteID AND sponsorID = @sponsorID)
		RAISERROR('Invalid Sponsor',16,1);

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	IF NOT EXISTS (select sponsorUsageID from dbo.sponsorsUsage where sponsorID = @sponsorID and referenceType = @referenceType and referenceID = @referenceID)
	BEGIN
		INSERT INTO dbo.sponsorsUsage (sponsorID, referenceType, referenceID, sponsorOrder, sponsorGroupingID)
		VALUES (@sponsorID, @referenceType, @referenceID, 999, CASE WHEN @sponsorGroupingID = 0 THEN NULL ELSE @sponsorGroupingID END);

		EXEC dbo.sponsors_reorderSponsorsInUsage @referenceType=@referenceType, @referenceID=@referenceID;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"auditLog", "d": {
			"AUDITCODE":"SPNSR",
			"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
			"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
			"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
			"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars('[' + sponsorName + '] was associated to ' + @referenceType + '-' + cast(@referenceID AS VARCHAR(20)) + '.'),'"','\"') + '" } }'
		FROM dbo.sponsors
		WHERE sponsorID = @sponsorID;
	END
	ELSE IF @sponsorGroupingID IS NOT NULL
	BEGIN
		-- Sponsor already exists, but update the group assignment if provided
		UPDATE dbo.sponsorsUsage
		SET sponsorGroupingID = CASE WHEN @sponsorGroupingID = 0 THEN NULL ELSE @sponsorGroupingID END
		WHERE sponsorID = @sponsorID
		AND referenceType = @referenceType
		AND referenceID = @referenceID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

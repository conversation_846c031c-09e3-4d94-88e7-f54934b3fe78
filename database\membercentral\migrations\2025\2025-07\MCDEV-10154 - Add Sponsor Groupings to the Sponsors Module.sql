-- MCDEV-10154 - Add Sponsor Groupings to the Sponsors Module
-- This migration adds sponsor grouping functionality similar to Event Rate Groupings
-- for both MemberCentral events and SeminarWeb programs

USE memberCentral;
GO

-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
-- (1) Create ev_sponsorGrouping TABLE for MemberCentral Events
-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

CREATE TABLE dbo.ev_sponsorGrouping(
	sponsorGroupingID INT IDENTITY(1,1) NOT NULL,
	eventID INT NOT NULL,
	sponsorGrouping VARCHAR(200) NOT NULL,
	sponsorGroupingOrder INT NOT NULL,
 CONSTRAINT PK_ev_sponsorGrouping PRIMARY KEY CLUSTERED
(
	sponsorGroupingID ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT IX_ev_sponsorGrouping UNIQUE NONCLUSTERED
(
	eventID ASC,
	sponsorGrouping ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE dbo.ev_sponsorGrouping WITH CHECK ADD CONSTRAINT FK_ev_sponsorGrouping_ev_events FOREIGN KEY(eventID)
REFERENCES dbo.ev_events (eventID)
GO

-- Add sponsorGroupingID column to link sponsors to groups
-- Following the Rates module pattern: existing sponsors remain with NULL sponsorGroupingID
-- which will be treated as "Default - No Grouping" (virtual ID 0) in stored procedures
ALTER TABLE dbo.sponsorsUsage ADD sponsorGroupingID INT NULL;
GO

-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
-- (4) Create SeminarWeb sponsor grouping table (executed from MemberCentral context)
-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
Use seminarweb
GO

-- Create sw_sponsorGrouping TABLE for SeminarWeb Programs
CREATE TABLE dbo.sw_sponsorGrouping(
	sponsorGroupingID INT IDENTITY(1,1) NOT NULL,
	seminarID INT NOT NULL,
	participantID INT NULL,
	sponsorGrouping VARCHAR(200) NOT NULL,
	sponsorGroupingOrder INT NOT NULL,
 CONSTRAINT PK_sw_sponsorGrouping PRIMARY KEY CLUSTERED
(
	sponsorGroupingID ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90) ON [PRIMARY],
 CONSTRAINT IX_sw_sponsorGrouping UNIQUE NONCLUSTERED
(
	seminarID ASC,
	participantID ASC,
	sponsorGrouping ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE dbo.sw_sponsorGrouping WITH CHECK ADD CONSTRAINT FK_sw_sponsorGrouping_tblSeminars FOREIGN KEY(seminarID)
REFERENCES dbo.tblSeminars (seminarID)
GO

ALTER TABLE dbo.sw_sponsorGrouping WITH CHECK ADD CONSTRAINT FK_sw_sponsorGrouping_tblParticipants FOREIGN KEY(participantID)
REFERENCES dbo.tblParticipants (participantID)
GO

-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
-- (2) Modify sponsorsUsage TABLE to add sponsorGroupingID column
-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
Use membercentral 
GO

-- Note: We don't add FK constraint yet as we need to support both ev_sponsorGrouping and sw_sponsorGrouping
-- The constraint will be handled at the application level

-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
-- (3) Create stored procedures for MemberCentral Events sponsor groupings
-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

-- Create sponsor grouping procedure (generalized for all contexts)
CREATE PROC dbo.sponsors_createSponsorGrouping
@referenceType varchar(50),
@referenceID int,
@sponsorGrouping varchar(200),
@sponsorGroupingID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @sponsorGroupingID = null;

	-- Determine context and execute appropriate logic
	IF @referenceType = 'Events'
	BEGIN
		-- Check if grouping already exists for Events
		IF EXISTS (SELECT sponsorGroupingID FROM dbo.ev_sponsorGrouping WHERE eventID=@referenceID AND sponsorGrouping=@sponsorGrouping)
			RAISERROR('Sponsor Grouping exists.',16,1);

		DECLARE @sponsorGroupingOrder int;
		SELECT @sponsorGroupingOrder = ISNULL(MAX(sponsorGroupingOrder),0)+1 FROM dbo.ev_sponsorGrouping WHERE eventID=@referenceID;

		INSERT INTO dbo.ev_sponsorGrouping (eventID, sponsorGrouping, sponsorGroupingOrder)
		VALUES (@referenceID, @sponsorGrouping, @sponsorGroupingOrder);

		SELECT @sponsorGroupingID = SCOPE_IDENTITY();
	END
	ELSE IF @referenceType IN ('swlProgram', 'swodProgram', 'swbprogram')
	BEGIN
		-- Check if grouping already exists for SeminarWeb
		IF EXISTS (SELECT sponsorGroupingID FROM seminarWeb.dbo.sw_sponsorGrouping WHERE seminarID=@referenceID AND participantID=0 AND sponsorGrouping=@sponsorGrouping)
			RAISERROR('Sponsor Grouping exists.',16,1);

		DECLARE @swSponsorGroupingOrder int;
		SELECT @swSponsorGroupingOrder = ISNULL(MAX(sponsorGroupingOrder),0)+1 FROM seminarWeb.dbo.sw_sponsorGrouping WHERE seminarID=@referenceID AND participantID=0;

		INSERT INTO seminarWeb.dbo.sw_sponsorGrouping (seminarID, participantID, sponsorGrouping, sponsorGroupingOrder)
		VALUES (@referenceID, 0, @sponsorGrouping, @swSponsorGroupingOrder);

		SELECT @sponsorGroupingID = SCOPE_IDENTITY();
	END
	ELSE
	BEGIN
		RAISERROR('Unsupported reference type: %s', 16, 1, @referenceType);
		RETURN -1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Update sponsor grouping procedure (generalized for all contexts)
CREATE PROC dbo.sponsors_updateSponsorGrouping
@referenceType varchar(50),
@sponsorGroupingID int,
@sponsorGrouping varchar(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Determine context and execute appropriate logic
	IF @referenceType = 'Events'
	BEGIN
		IF NOT EXISTS (SELECT sponsorGroupingID FROM dbo.ev_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID)
			RAISERROR('Sponsor Grouping does not exist.',16,1);

		DECLARE @eventID int;
		SELECT @eventID = eventID FROM dbo.ev_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID;

		IF EXISTS (SELECT sponsorGroupingID FROM dbo.ev_sponsorGrouping WHERE eventID=@eventID AND sponsorGrouping=@sponsorGrouping AND sponsorGroupingID != @sponsorGroupingID)
			RAISERROR('Sponsor Grouping name already exists.',16,1);

		UPDATE dbo.ev_sponsorGrouping
		SET sponsorGrouping = @sponsorGrouping
		WHERE sponsorGroupingID = @sponsorGroupingID;
	END
	ELSE IF @referenceType IN ('swlProgram', 'swodProgram', 'swbprogram')
	BEGIN
		IF NOT EXISTS (SELECT sponsorGroupingID FROM seminarWeb.dbo.sw_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID)
			RAISERROR('Sponsor Grouping does not exist.',16,1);

		DECLARE @seminarID int, @participantID int;
		SELECT @seminarID = seminarID, @participantID = participantID FROM seminarWeb.dbo.sw_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID;

		IF EXISTS (SELECT sponsorGroupingID FROM seminarWeb.dbo.sw_sponsorGrouping WHERE seminarID=@seminarID AND participantID=@participantID AND sponsorGrouping=@sponsorGrouping AND sponsorGroupingID != @sponsorGroupingID)
			RAISERROR('Sponsor Grouping name already exists.',16,1);

		UPDATE seminarWeb.dbo.sw_sponsorGrouping
		SET sponsorGrouping = @sponsorGrouping
		WHERE sponsorGroupingID = @sponsorGroupingID;
	END
	ELSE
	BEGIN
		RAISERROR('Unsupported reference type: %s', 16, 1, @referenceType);
		RETURN -1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Reorder sponsor groupings procedure (generalized for all contexts)
CREATE PROC dbo.sponsors_reorderSponsorGroupings
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Determine context and execute appropriate logic
	IF @referenceType = 'Events'
	BEGIN
		DECLARE @tmpEvents TABLE (neworder int NOT NULL, sponsorGroupingID int NOT NULL, sponsorGroupingOrder int NOT NULL);

		INSERT INTO @tmpEvents (sponsorGroupingID, sponsorGroupingOrder, newOrder)
		SELECT sponsorGroupingID, sponsorGroupingOrder, ROW_NUMBER() OVER(ORDER BY sponsorGroupingOrder) as newOrder
		FROM dbo.ev_sponsorGrouping
		WHERE eventID = @referenceID;

		UPDATE sg
		SET sg.sponsorGroupingOrder = t.neworder
		FROM dbo.ev_sponsorGrouping as sg
		INNER JOIN @tmpEvents as t on sg.sponsorGroupingID = t.sponsorGroupingID;
	END
	ELSE IF @referenceType IN ('swlProgram', 'swodProgram', 'swbprogram')
	BEGIN
		DECLARE @tmpSW TABLE (neworder int NOT NULL, sponsorGroupingID int NOT NULL, sponsorGroupingOrder int NOT NULL);

		INSERT INTO @tmpSW (sponsorGroupingID, sponsorGroupingOrder, newOrder)
		SELECT sponsorGroupingID, sponsorGroupingOrder, ROW_NUMBER() OVER(ORDER BY sponsorGroupingOrder) as newOrder
		FROM seminarWeb.dbo.sw_sponsorGrouping
		WHERE seminarID = @referenceID AND participantID = 0;

		UPDATE sg
		SET sg.sponsorGroupingOrder = t.neworder
		FROM seminarWeb.dbo.sw_sponsorGrouping as sg
		INNER JOIN @tmpSW as t on sg.sponsorGroupingID = t.sponsorGroupingID
		WHERE sg.participantID = 0;
	END
	ELSE
	BEGIN
		RAISERROR('Unsupported reference type: %s', 16, 1, @referenceType);
		RETURN -1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Delete sponsor grouping procedure (generalized for all contexts)
CREATE PROC dbo.sponsors_deleteSponsorGrouping
@referenceType varchar(50),
@sponsorGroupingID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Check if any sponsors are using this grouping (common check)
	IF EXISTS (SELECT sponsorUsageID FROM dbo.sponsorsUsage WHERE sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Cannot delete sponsor grouping that has sponsors assigned to it.',16,1);

	-- Determine context and execute appropriate logic
	IF @referenceType = 'Events'
	BEGIN
		IF NOT EXISTS (SELECT sponsorGroupingID FROM dbo.ev_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID)
			RAISERROR('Sponsor Grouping does not exist.',16,1);

		DECLARE @eventID int;
		SELECT @eventID = eventID FROM dbo.ev_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID;

		DELETE FROM dbo.ev_sponsorGrouping WHERE sponsorGroupingID = @sponsorGroupingID;

		-- Reorder remaining groupings
		EXEC dbo.sponsors_reorderSponsorGroupings @referenceType='Events', @referenceID=@eventID;
	END
	ELSE IF @referenceType IN ('swlProgram', 'swodProgram', 'swbprogram')
	BEGIN
		IF NOT EXISTS (SELECT sponsorGroupingID FROM seminarWeb.dbo.sw_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID)
			RAISERROR('Sponsor Grouping does not exist.',16,1);

		DECLARE @seminarID int, @participantID int;
		SELECT @seminarID = seminarID, @participantID = participantID FROM seminarWeb.dbo.sw_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID;

		DELETE FROM seminarWeb.dbo.sw_sponsorGrouping WHERE sponsorGroupingID = @sponsorGroupingID;

		-- Reorder remaining groupings
		EXEC dbo.sponsors_reorderSponsorGroupings @referenceType=@referenceType, @referenceID=@seminarID;
	END
	ELSE
	BEGIN
		RAISERROR('Unsupported reference type: %s', 16, 1, @referenceType);
		RETURN -1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Move sponsor grouping procedure (generalized for all contexts)
CREATE PROC dbo.sponsors_moveSponsorGrouping
@referenceType varchar(50),
@sponsorGroupingID int,
@direction varchar(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF @direction NOT IN ('up','down')
		RAISERROR('Invalid direction. Must be up or down.',16,1);

	-- Determine context and execute appropriate logic
	IF @referenceType = 'Events'
	BEGIN
		DECLARE @eventID int, @currentOrder int, @swapOrder int, @swapGroupingID int;

		SELECT @eventID = eventID, @currentOrder = sponsorGroupingOrder
		FROM dbo.ev_sponsorGrouping
		WHERE sponsorGroupingID = @sponsorGroupingID;

		IF @eventID IS NULL
			RAISERROR('Sponsor Grouping does not exist.',16,1);

		-- Find the grouping to swap with
		IF @direction = 'up'
		BEGIN
			SELECT TOP 1 @swapGroupingID = sponsorGroupingID, @swapOrder = sponsorGroupingOrder
			FROM dbo.ev_sponsorGrouping
			WHERE eventID = @eventID AND sponsorGroupingOrder < @currentOrder
			ORDER BY sponsorGroupingOrder DESC;
		END
		ELSE
		BEGIN
			SELECT TOP 1 @swapGroupingID = sponsorGroupingID, @swapOrder = sponsorGroupingOrder
			FROM dbo.ev_sponsorGrouping
			WHERE eventID = @eventID AND sponsorGroupingOrder > @currentOrder
			ORDER BY sponsorGroupingOrder ASC;
		END

		-- Perform the swap if we found a grouping to swap with
		IF @swapGroupingID IS NOT NULL
		BEGIN
			UPDATE dbo.ev_sponsorGrouping SET sponsorGroupingOrder = @swapOrder WHERE sponsorGroupingID = @sponsorGroupingID;
			UPDATE dbo.ev_sponsorGrouping SET sponsorGroupingOrder = @currentOrder WHERE sponsorGroupingID = @swapGroupingID;
		END
	END
	ELSE IF @referenceType IN ('swlProgram', 'swodProgram', 'swbprogram')
	BEGIN
		DECLARE @seminarID int, @participantID int, @swCurrentOrder int, @swSwapOrder int, @swSwapGroupingID int;

		SELECT @seminarID = seminarID, @participantID = participantID, @swCurrentOrder = sponsorGroupingOrder
		FROM seminarWeb.dbo.sw_sponsorGrouping
		WHERE sponsorGroupingID = @sponsorGroupingID;

		IF @seminarID IS NULL
			RAISERROR('Sponsor Grouping does not exist.',16,1);

		-- Find the grouping to swap with
		IF @direction = 'up'
		BEGIN
			SELECT TOP 1 @swSwapGroupingID = sponsorGroupingID, @swSwapOrder = sponsorGroupingOrder
			FROM seminarWeb.dbo.sw_sponsorGrouping
			WHERE seminarID = @seminarID AND participantID = @participantID AND sponsorGroupingOrder < @swCurrentOrder
			ORDER BY sponsorGroupingOrder DESC;
		END
		ELSE
		BEGIN
			SELECT TOP 1 @swSwapGroupingID = sponsorGroupingID, @swSwapOrder = sponsorGroupingOrder
			FROM seminarWeb.dbo.sw_sponsorGrouping
			WHERE seminarID = @seminarID AND participantID = @participantID AND sponsorGroupingOrder > @swCurrentOrder
			ORDER BY sponsorGroupingOrder ASC;
		END

		-- Perform the swap if we found a grouping to swap with
		IF @swSwapGroupingID IS NOT NULL
		BEGIN
			UPDATE seminarWeb.dbo.sw_sponsorGrouping SET sponsorGroupingOrder = @swSwapOrder WHERE sponsorGroupingID = @sponsorGroupingID;
			UPDATE seminarWeb.dbo.sw_sponsorGrouping SET sponsorGroupingOrder = @swCurrentOrder WHERE sponsorGroupingID = @swSwapGroupingID;
		END
	END
	ELSE
	BEGIN
		RAISERROR('Unsupported reference type: %s', 16, 1, @referenceType);
		RETURN -1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Get sponsor groupings (generalized for all contexts)
CREATE PROC dbo.sponsors_getSponsorGroupings
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Determine context and execute appropriate logic
	IF @referenceType = 'Events'
	BEGIN
		SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
		FROM dbo.ev_sponsorGrouping
		WHERE eventID = @referenceID
		ORDER BY sponsorGroupingOrder;
	END
	ELSE IF @referenceType IN ('swlProgram', 'swodProgram', 'swbprogram')
	BEGIN
		SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
		FROM seminarWeb.dbo.sw_sponsorGrouping
		WHERE seminarID = @referenceID
		ORDER BY sponsorGroupingOrder;
	END
	ELSE
	BEGIN
		RAISERROR('Unsupported reference type: %s', 16, 1, @referenceType);
		RETURN -1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Update the existing sponsors_getSponsorsByReferenceIDFull procedure to include grouping information
ALTER PROC dbo.sponsors_getSponsorsByReferenceIDFull
@siteID INT,
@referenceType VARCHAR(20),
@referenceID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @featureImageConfigReferenceType VARCHAR(30) = 'evSiteSponsor';
	DECLARE @featureImageReferenceType VARCHAR(30) = 'EventSponsors';

	-- For Events, get sponsor groupings from ev_sponsorGrouping
	IF @referenceType = 'Events'
	BEGIN
		SELECT su.sponsorUsageID, s.sponsorID, s.sponsorName, s.sponsorURL, s.sponsorContentId,
			sponsorContent.rawContent as sponsorContent, ficu.featureImageConfigID, fiu.featureImageID,
			fics.fileExtension, ficus.featureImageSizeID, su.sponsorOrder,
			ISNULL(esg.sponsorGroupingID, 0) AS sponsorGroupingID, esg.sponsorGrouping, esg.sponsorGroupingOrder,ISNULL(esg.sponsorGroupingOrder, 999) AS sponsorGroupingOrderDefault
		FROM dbo.sponsorsUsage as su
		INNER JOIN dbo.sponsors as s on s.siteID = @siteID and s.sponsorID = su.sponsorID
		LEFT OUTER JOIN dbo.ev_sponsorGrouping as esg on esg.sponsorGroupingID = su.sponsorGroupingID
		LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = s.siteID AND ficu.referenceType = @featureImageConfigReferenceType
		LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu ON fiu.featureImageConfigID = ficu.featureImageConfigID AND fiu.referenceID = s.sponsorID
			AND fiu.referenceType = @featureImageReferenceType
		LEFT OUTER JOIN dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
		LEFT OUTER JOIN dbo.cms_featuredImageConfigUsagesAndSizes AS ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
		AND ficus.referenceType = 'viewEventDetails'
		LEFT OUTER JOIN dbo.cms_featuredImageConfigSizes AS fics ON fics.featureImageConfigID = ficus.featureImageSizeID
		CROSS APPLY dbo.fn_getContent(s.sponsorcontentID,1) AS sponsorContent
		WHERE su.referenceType = @referenceType
		AND su.referenceID = @referenceID
		ORDER BY ISNULL(esg.sponsorGroupingOrder, 999), su.sponsorOrder;
	END
	-- For SeminarWeb, get sponsor groupings from sw_sponsorGrouping
	ELSE IF @referenceType IN ('swlProgram', 'swodProgram', 'swbprogram')
	BEGIN
		SELECT su.sponsorUsageID, s.sponsorID, s.sponsorName, s.sponsorURL, s.sponsorContentId,
			sponsorContent.rawContent as sponsorContent, ficu.featureImageConfigID, fiu.featureImageID,
			fics.fileExtension, ficus.featureImageSizeID, su.sponsorOrder,
			ISNULL(swsg.sponsorGroupingID, 0) AS sponsorGroupingID, swsg.sponsorGrouping, swsg.sponsorGroupingOrder,ISNULL(swsg.sponsorGroupingOrder, 999) AS sponsorGroupingOrderDefault
		FROM dbo.sponsorsUsage as su
		INNER JOIN dbo.sponsors as s on s.siteID = @siteID and s.sponsorID = su.sponsorID
		LEFT OUTER JOIN seminarWeb.dbo.sw_sponsorGrouping as swsg on swsg.sponsorGroupingID = su.sponsorGroupingID
		LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = s.siteID AND ficu.referenceType = @featureImageConfigReferenceType
		LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu ON fiu.featureImageConfigID = ficu.featureImageConfigID AND fiu.referenceID = s.sponsorID
			AND fiu.referenceType = @featureImageReferenceType
		LEFT OUTER JOIN dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
		LEFT OUTER JOIN dbo.cms_featuredImageConfigUsagesAndSizes AS ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
		AND ficus.referenceType = 'viewSemwebDetails'
		LEFT OUTER JOIN dbo.cms_featuredImageConfigSizes AS fics ON  fics.featureImageSizeID = ficus.featureImageSizeID
		CROSS APPLY dbo.fn_getContent(s.sponsorcontentID,1) AS sponsorContent
		WHERE su.referenceType = @referenceType
		AND su.referenceID = @referenceID
		ORDER BY ISNULL(swsg.sponsorGroupingOrder, 999), su.sponsorOrder;
	END
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
-- (6) Add moveSponsorWithinGroup procedure for reordering sponsors within groups
-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

-- Move sponsor within group procedure
CREATE PROC dbo.sponsors_moveSponsorWithinGroup
@sponsorUsageID int,
@sponsorGroupingID int,
@direction varchar(10),
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;

BEGIN TRY
	BEGIN TRANSACTION;

	DECLARE @currentOrder int
	DECLARE @targetOrder int
	DECLARE @targetUsageID int

	-- Get current sponsor order
	SELECT @currentOrder = sponsorOrder
	FROM dbo.sponsorsUsage
	WHERE sponsorUsageID = @sponsorUsageID

	-- Calculate target order based on direction
	IF @direction = 'up'
		SET @targetOrder = @currentOrder - 1
	ELSE
		SET @targetOrder = @currentOrder + 1

	-- Find the sponsor at the target position within the same group
	SELECT @targetUsageID = sponsorUsageID
	FROM dbo.sponsorsUsage
	WHERE referenceType = @referenceType
	AND referenceID = @referenceID
	AND sponsorOrder = @targetOrder
	AND ISNULL(sponsorGroupingID, 0) = ISNULL(@sponsorGroupingID, 0)

	-- Swap positions if target exists
	IF @targetUsageID IS NOT NULL
	BEGIN
		-- Update target sponsor order
		UPDATE dbo.sponsorsUsage
		SET sponsorOrder = @currentOrder
		WHERE sponsorUsageID = @targetUsageID

		-- Update current sponsor order
		UPDATE dbo.sponsorsUsage
		SET sponsorOrder = @targetOrder
		WHERE sponsorUsageID = @sponsorUsageID
	END

	COMMIT TRANSACTION;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
-- (7) Add AJAX component registrations and security rights using project standard
-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

-- Component methods access rights for sponsor grouping functionality
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    DECLARE @componentID int, @adminViewRTFID int;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;
	CREATE TABLE #ajaxComponentMethods (autoid int IDENTITY(1,1), methodName varchar(500), resourceTypeFunctionID int, methodID int);

	SELECT @adminViewRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES
		('getSponsorsWithGroupings', @adminViewRTFID),
		('createSponsorGrouping', @adminViewRTFID),
		('updateSponsorGrouping', @adminViewRTFID),
		('moveSponsorGrouping', @adminViewRTFID),
		('deleteSponsorGrouping', @adminViewRTFID),
		('moveSponsorWithinGroup', @adminViewRTFID);

	EXEC dbo.ajax_addComponentMethodRightsBulk
		@componentName='SPONSORS',
		@requestCFC='model.admin.common.modules.sponsors.sponsors',
		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO
